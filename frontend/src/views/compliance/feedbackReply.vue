<template>
  <div class="zgsTableManage-class">
    <div class="head-class">
      <el-row :gutter="12">
        <el-col :span="4">
          <el-input v-model="param.answerAndQuestion" placeholder="请输入问题/回复关键字"></el-input>
        </el-col>
        <el-col :span="4">
          <el-select-multiple
              v-model="param.feedbackTypeList"
              style="width: 100%"
              placeholder="反馈类型"
              ref="feedbackTypeList"
              clearable
              confirm>
            <el-select-multiple-option
                v-for="(item,index) in feedbackTypeOptions"
                :key="index"
                :label="item.label"
                :value="item.value">
            </el-select-multiple-option>
          </el-select-multiple>
        </el-col>
        <el-col :span="4">
          <el-input v-model="param.replyName" placeholder="回复人"></el-input>
        </el-col>
        <el-col :span="4">
          <el-input v-model="param.feedbackName" placeholder="用户姓名"></el-input>
        </el-col>
        <el-col :span="8">
          <el-date-picker
              style="width: 100%;"
              v-model="param.createTimeList"
              type="datetimerange"
              :picker-options="pickerOptions"
              start-placeholder="首次反馈时间"
              end-placeholder="首次反馈时间"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              @change="handleDateChange">
          </el-date-picker>
        </el-col>
      </el-row>
    </div>
    <el-row style="margin-bottom: 10px; align-items: center;">
      <el-col :span="12">
        <span style="font-size: 14px;color: #666666;font-weight: 400;">共 <span style="color: #333333">{{tableTotal}}</span> 条</span>
      </el-col>
      <el-col :span="12" class="button-group-col">
        <div class="button-group">
          <el-button size="small" type="primary" @click="dosearch()">查询</el-button>
          <el-button size="small" type="primary" plain @click="clearForm">重置</el-button>
          <el-button size="small" type="primary" plain @click="exportFieldList">导出</el-button>
        </div>
      </el-col>
    </el-row>
    <!--  数据表格-->
    <div class="body-class">
      <el-table :data="tableData" ref="multipleTable" style="width: 100%">
        <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
        <el-table-column label="问题&回复" min-width="400">
          <template slot-scope="scope">
            <div class="question-answer-display">
              <!-- 问题部分 -->
              <div class="question-section">
                <el-tooltip
                  open-delay="500"
                  :content="scope.row.question || '--'"
                  placement="top"
                  :disabled="!scope.row.question"
                  :visible-arrow="false"
                  effect="light">
                  <div class="question-line">
                    <span class="content text-ellipsis-1"><span class="label">问：</span>{{ scope.row.question || '--' }}</span>
                  </div>
                </el-tooltip>
              </div>
              <!-- 答案部分 -->
              <div class="answer-section">
                <el-tooltip
                  open-delay="500"
                  :content="scope.row.answer || '--'"
                  placement="top"
                  :disabled="!scope.row.answer"
                  :visible-arrow="false"
                  effect="light">
                  <div class="answer-line">
                    <span class="content text-ellipsis-2"><span class="label">答：</span>{{ scope.row.answer || '--' }}</span>
                  </div>
                </el-tooltip>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="反馈内容" width="200">
          <template slot-scope="scope">
            <el-tooltip
              open-delay="500"
              :content="scope.row.feedbackContent || '--'"
              placement="top"
              :disabled="!scope.row.feedbackContent || scope.row.feedbackContent === '--'"
              :visible-arrow="false"
              effect="light">
              <div class="text-ellipsis-3">
                {{ scope.row.feedbackContent ? scope.row.feedbackContent : '--' }}
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="反馈类型" width="100" align="center" :formatter="feedbackTypeFormatter"></el-table-column>
        <el-table-column label="首次反馈时间" width="120" align="center">
          <template slot-scope="scope">
            <div v-if="scope.row.createTime" class="time-display">
              <div class="date-line">{{ formatDate(scope.row.createTime) }}</div>
              <div class="time-line">{{ formatTime(scope.row.createTime) }}</div>
            </div>
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="userName" label="用户ID" width="100" >
          <template scope="scope">
            <span>{{ scope.row.userName ? scope.row.userName : '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="feedbackName" label="用户姓名" width="100" >
          <template scope="scope">
            <span>{{ scope.row.feedbackName ? scope.row.feedbackName : '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="telephone" label="手机号码" width="160" >
          <template scope="scope">
            <span>{{ scope.row.telephone ? scope.row.telephone : '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="isReply" label="是否答复" width="100" >
          <template scope="scope">
            <span>
              {{ scope.row.isReply ? scope.row.isReply === '0' ? '否' : '是' : '--' }}
            </span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="replyName" label="答复人" width="100" >
          <template scope="scope">
            <span>
              {{ scope.row.replyName ? scope.row.replyName : '--' }}
            </span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="newReplyTime" label="最新答复时间" width="160">
          <template slot-scope="scope">
            <div v-if="scope.row.newReplyTime" class="time-display">
              <div class="date-line">{{ formatDate(scope.row.newReplyTime) }}</div>
              <div class="time-line">{{ formatTime(scope.row.newReplyTime) }}</div>
            </div>
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" width="180">
          <template scope="scope">
            <span v-if="scope.row.isReply === '0'">
              <span v-if="scope.row.isFeedbackNew === '1'">
                <div class="source-marker">NEW</div>
              </span>
              <el-link type="primary" :underline="false" @click="reply(scope.row)">答复</el-link>
            </span>
            <el-link type="primary" :underline="false" @click="replyHistory(scope.row)">历史答复</el-link>
            <el-link type="primary" :underline="false" @click="deleteRow(scope.row)">删除</el-link>
          </template>
        </el-table-column>
      </el-table>
    <!-- 分页 -->
    <el-pagination
        style="text-align: center; margin-top: 10px;"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="param.startRow"
        :page-sizes="[10, 20, 50, 200]"
        :page-size="param.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tableTotal">
    </el-pagination>
    </div>

    <!-- 答复详情对话框 -->
    <reply-detail-dialog
      v-if="replyDetailDialogVisible"
      :visible.sync="replyDetailDialogVisible"
      :feedback-data="selectedFeedbackData"
      :dialog-type="replyDetailDialogType"
      @close="handleReplyDetailDialogClose"
      @submit-success="handleReplySubmitSuccess"
    />
  </div>
</template>
<script>
import {_exportFieldList, _getTableList} from "@/api/feedbackReply-api";
import {_deleteFeedback} from "@/api/chat";
import ReplyDetailDialog from "@/views/compliance/wenwen/replyDetailDialog.vue";

export default {
  name: 'feedbackReply',
  components: {
    ReplyDetailDialog
  },
  data() {
    return {
      param: {
        pageSize: 10,
        startRow: 1,
        answerAndQuestion: "",
        feedbackTypeList: [],
        replyName: "",
        feedbackName: "",
        createTimeList: []
      },
      feedbackTypeOptions: [{
        value: '1',
        label: '回答有误'
      }, {
        value: '2',
        label: '响应慢'
      }, {
        value: '3',
        label: '案例有误'
      }, {
        value: '4',
        label: '法规有误'
      }, {
        value: '0',
        label: '其它'
      }],
      pickerOptions: {
        shortcuts: [{
          text: '今天',
          onClick (picker) {
            const start = new Date();
            start.setHours(0, 0, 0, 0); // 设置为当天0点
            const end = new Date();
            end.setHours(23, 59, 59, 999); // 设置为当天23:59:59
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      tableData: [],
      tableTotal: 0,
      // 答复详情页面相关数据
      replyDetailDialogVisible: false,
      selectedFeedbackData: {},
      replyDetailDialogType: 'history' // 'history' 或 'reply'
    }
  },
  created() {
    this.dosearch()
  },
  methods: {
    dosearch(){
      this.param.startRow = 1
      this.search()
    },
    search() {
      let param = {
        question: this.param.answerAndQuestion,
        answer: this.param.answerAndQuestion,
        feedbackTypeList: this.param.feedbackTypeList,
        replyName: this.param.replyName,
        feedbackName: this.param.feedbackName,
        startCreateTime: this.param.createTimeList[0],
        endCreateTime: this.param.createTimeList[1],
        startRow: this.param.startRow,
        pageSize: this.param.pageSize
      }
      _getTableList(param).then(res => {
        this.tableData = res.data.result.tableList
        this.tableTotal = res.data.result.totalSize
        if (res.data.result.tableList.length === 0 && res.data.result.totalSize !== 0) {
          param.startRow = 1
          _getTableList(param).then(res => {
            this.tableData = res.data.result.tableList
            this.tableTotal = res.data.result.totalSize
          })
        }
      })
    },
    handleCurrentChange(val) {
      this.param.startRow = val
      this.search()
    },
    handleSizeChange(val) {
      this.param.pageSize = val
      this.param.startRow = 1
      this.search();
    },
    handleDateChange(val) {
      if (val && val.length === 2) {
        this.param.startTime = val[0]; // 开始时间
        this.param.endTime = val[1];   // 结束时间
      } else {
        this.param.startTime = "";
        this.param.endTime = "";
      }
    },
    clearForm() {
      this.param.answerAndQuestion = ""
      this.param.feedbackTypeList = []
      this.$refs.feedbackTypeList.clear();
      this.param.replyName = ""
      this.param.feedbackName = ""
      this.param.createTimeList = []
      this.param.pageSize = 10
      this.param.startRow = 1
      this.search()
    },
    exportFieldList() {
        if (this.tableData && this.tableData.length > 0) {
          let param = {
            question: this.param.answerAndQuestion,
            answer: this.param.answerAndQuestion,
            feedbackTypeList: this.param.feedbackTypeList,
            replyName: this.param.replyName,
            feedbackName: this.param.feedbackName,
            startCreateTime: this.param.createTimeList[0],
            endCreateTime: this.param.createTimeList[1],
            startRow: this.param.startRow,
            pageSize: this.param.pageSize
          }
          _exportFieldList(param)
        } else {
          this.$message.warning('当前表格没有数据可导出');
      }
    },
    feedbackTypeFormatter (row, column, cellValue, index) {
      let feedbackType = this.feedbackTypeOptions.find(item => {
        return item.value === row.feedbackType;
      })
      if (feedbackType) {
        return feedbackType.label
      } else {
        return '--'
      }
    },
    formatDate(dateTimeStr) {
      if (!dateTimeStr) return '';
      // 提取年月日部分 (YYYY-MM-DD)
      return dateTimeStr.split(' ')[0];
    },
    formatTime(dateTimeStr) {
      if (!dateTimeStr) return '';
      // 提取时分秒部分 (HH:mm:ss)
      return dateTimeStr.split(' ')[1] || '';
    },
    reply (row) {
      // 打开答复详情页面，显示回复框
      this.selectedFeedbackData = row;
      this.replyDetailDialogType = 'reply';
      this.replyDetailDialogVisible = true;
    },
    replyHistory (row) {
      // 打开答复详情页面，查看历史答复
      this.selectedFeedbackData = row;
      this.replyDetailDialogType = 'history';
      this.replyDetailDialogVisible = true;
    },
    deleteRow (row) {
      this.$confirm('删除后不可恢复，请确认是否删除。', '删除所选反馈', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        _deleteFeedback(row.chatContentId).then(res => {
          if (res.data.result) {
            this.$message({
              type: 'success',
              message: '删除成功'
            });
            this.search();
          } else {
            this.$message({
              type: 'warning',
              message: '删除失败'
            });
          }
        })
      })
    },
    handleReplyDetailDialogClose() {
      this.replyDetailDialogVisible = false;
      this.selectedFeedbackData = {};
      this.search()
    },
    handleReplySubmitSuccess() {
      this.replyDetailDialogVisible = false;
      this.selectedFeedbackData = {};
      this.search();
    }
  },
}
</script>
<style scoped lang="scss">
.zgsTableManage-class {
  padding: 10px 20px;
  .head-class {
    margin-bottom: 10px;
  }

  .button-group-col {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }

  /* 确保统计信息行的垂直居中对齐 */
  ::v-deep .el-row[style*="align-items: center"] {
    display: flex !important;
    align-items: center !important;
  }
  ::v-deep .el-date-editor .el-range-input.large{
    background-color: #F6F7F9 !important
  }
  .button-group{
    display: flex;
    gap: 8px;
  }

  /deep/ .el-input__inner {
    background-color: #F6F7F9 !important; /* 浅灰色背景 */
    border: none; /* 浅灰色边框 */
    color: #333 !important;
  }
  .flex-container {
    display: flex;
    justify-content: space-between;
    justify-content: flex-end;
    align-items: center;
    box-sizing: border-box;
    }
  ::v-deep .el-link--inner {
    border-bottom: none !important;
  }
  .text-ellipsis-1 {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.4;
    max-height: 1.4em; /* 1行 * 1.4行高 */
    word-break: break-word;
  }

  .text-ellipsis-2 {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.4;
    max-height: 2.8em; /* 2行 * 1.4行高 */
    word-break: break-word;
  }

  .text-ellipsis-3 {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
    max-height: 4.2em; /* 3行 * 1.4行高 */
    word-break: break-word;
    font-weight: 400;
    font-size: 14px;
    line-height: 18px;
    color: #000000;
  }

  .time-display {
    .date-line {
      font-weight: 400;
      font-size: 14px;
      color: #000000;
      line-height: 18px;
      margin-bottom: 2px;
    }

    .time-line {
      font-weight: 400;
      font-size: 14px;
      color: #000000;
      line-height: 18px;
    }
  }

  .question-answer-display {
    text-align: left;

    .question-section {

      .question-line {
        display: flex;
        align-items: flex-start;

        .label {
          color: #999999;
          font-weight: 400;
          font-size: 14px;
        }

        .content {
          color: #000000;
          font-weight: 400;
          font-size: 14px;
        }
      }
    }

    .answer-section {
      .answer-line {
        display: flex;
        align-items: flex-start;

        .label {
          color: #999999;
          font-weight: 400;
          font-size: 14px;
        }

        .content {
          color: #000000;
          font-weight: 400;
          font-size: 14px;
        }
      }
    }
  }
  .source-marker {
    color: white;
    font-size: 12px;
    position: absolute;
    width: 30px;
    height: 12px;
    top: 18px;
    left: 55px;
    line-height: 1;
    background-color: red;
  }

  .source-marker::before {
    content: '';
    position: absolute;
    top: 100%;
    left: 0;
    width: 0;
    height: 0;
    border-top: 6px solid red;
    border-right: 6px solid transparent;
  }
  .el-link.el-link--primary {
    border-color: #D50212;
    color: #D50212;
    font-size: 12px;
    transition: color 0.3s ease;
  }

  .el-link.el-link--primary:hover {
    color: #B8020F;
    text-decoration: underline;
  }

  .el-link.el-link--primary:active {
    color: #A0010D;
  }
}
</style>